#!/bin/bash
set -e

echo "🔄 Resetting PostgreSQL setup on EC2..."

# Stop PostgreSQL service
echo "Stopping PostgreSQL service..."
sudo systemctl stop postgresql || true

# Remove PostgreSQL completely
echo "Removing PostgreSQL installation..."
sudo apt remove --purge postgresql postgresql-* -y
sudo apt autoremove -y
sudo apt autoclean

# Remove PostgreSQL data directories
echo "Removing PostgreSQL data directories..."
sudo rm -rf /var/lib/postgresql/
sudo rm -rf /etc/postgresql/
sudo rm -rf /var/log/postgresql/

# Remove postgres user
echo "Removing postgres system user..."
sudo deluser postgres || true
sudo delgroup postgres || true

# Clean up any remaining processes
echo "Cleaning up processes..."
sudo pkill -f postgres || true

echo "✅ PostgreSQL completely removed"

# Fresh installation
echo "🆕 Installing PostgreSQL fresh..."
sudo apt update
sudo apt install -y postgresql postgresql-contrib

# Start PostgreSQL
echo "Starting PostgreSQL service..."
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to start..."
sleep 5

# Get PostgreSQL version and config directory
PG_VERSION=$(dpkg -l | grep postgresql-[0-9] | head -1 | grep -oP "postgresql-\K\d+" || echo "15")
PG_CONFIG_DIR="/etc/postgresql/${PG_VERSION}/main"

echo "PostgreSQL version: $PG_VERSION"
echo "Config directory: $PG_CONFIG_DIR"

# Configure authentication to trust temporarily
echo "Configuring temporary trust authentication..."
sudo cp "$PG_CONFIG_DIR/pg_hba.conf" "$PG_CONFIG_DIR/pg_hba.conf.original"

# Set all local connections to trust
sudo sed -i 's/local   all             postgres                                peer/local   all             postgres                                trust/' "$PG_CONFIG_DIR/pg_hba.conf"
sudo sed -i 's/local   all             all                                     peer/local   all             all                                     trust/' "$PG_CONFIG_DIR/pg_hba.conf"

# Restart to apply changes
sudo systemctl restart postgresql
sleep 5

# Set new password for postgres user
echo "Setting new password for postgres user..."
NEW_PASSWORD="Kingragnar1\$"
sudo -u postgres psql -c "ALTER USER postgres PASSWORD '$NEW_PASSWORD';"

# Create new database
echo "Creating sheepskin database..."
sudo -u postgres dropdb sheepskin 2>/dev/null || true
sudo -u postgres createdb sheepskin

# Configure for password authentication
echo "Configuring password authentication..."
sudo sed -i 's/local   all             postgres                                trust/local   all             postgres                                md5/' "$PG_CONFIG_DIR/pg_hba.conf"
sudo sed -i 's/local   all             all                                     trust/local   all             all                                     md5/' "$PG_CONFIG_DIR/pg_hba.conf"

# Add host connections
if ! sudo grep -q "host.*all.*postgres.*127.0.0.1/32.*md5" "$PG_CONFIG_DIR/pg_hba.conf"; then
    echo "host    all             postgres        127.0.0.1/32            md5" | sudo tee -a "$PG_CONFIG_DIR/pg_hba.conf"
fi

# Restart PostgreSQL
sudo systemctl restart postgresql
sleep 5

# Test connection
echo "Testing database connection..."
if PGPASSWORD="$NEW_PASSWORD" psql -h localhost -U postgres -d sheepskin -c "SELECT 1;" >/dev/null 2>&1; then
    echo "✅ Database connection successful!"
    echo "✅ PostgreSQL reset complete!"
    echo ""
    echo "Database details:"
    echo "- Host: localhost"
    echo "- Port: 5432"
    echo "- Database: sheepskin"
    echo "- Username: postgres"
    echo "- Password: Kingragnar1\$"
    echo ""
    echo "DATABASE_URL: postgresql://postgres:Kingragnar1\$@localhost:5432/sheepskin"
else
    echo "❌ Database connection failed!"
    exit 1
fi
