name: CI/CD Pipeline for Sheepskin Collection Platform

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

env:
  NODE_VERSION: "18"
  APP_NAME: "sheepskin-app"
  APP_DIRECTORY: "/home/<USER>/sheepskin-app"
  REPO_URL: "https://github.com/khairiEsprit/sheepskin.git"

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: sheepskin_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Set up test environment
        run: |
          cat > .env.test << EOF
          DATABASE_URL="postgresql://postgres:postgres@localhost:5432/sheepskin_test"
          NEXTAUTH_SECRET="test-secret-key-for-ci"
          NEXTAUTH_URL="http://localhost:3000"
          NODE_ENV="test"
          EOF

      - name: Generate Prisma client
        run: npx prisma generate

      - name: Run database migrations
        run: npx prisma migrate deploy
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/sheepskin_test"

      - name: Run TypeScript check
        run: npx tsc --noEmit

      - name: Run tests
        run: npm test
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/sheepskin_test"

      - name: Build app
        run: npm run build
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/sheepskin_test"
          NEXTAUTH_SECRET: "test-secret-key-for-ci"
          NEXTAUTH_URL: "http://localhost:3000"

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: next-build
          path: |
            .next/
            package.json
            package-lock.json
            prisma/
            public/
          retention-days: 1

      - name: Run dependency security audit
        run: npm audit --audit-level moderate

  deploy:
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: next-build

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Debug secrets and repository access
        env:
          PAT_TOKEN: ${{ secrets.PAT_TOKEN }}
          EC2_HOST: ${{ secrets.EC2_HOST }}
          EC2_USER: ${{ secrets.EC2_USER }}
          EC2_KEY: ${{ secrets.EC2_KEY }}
          NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
          NEXTAUTH_URL: ${{ secrets.NEXTAUTH_URL }}
        run: |
          echo "Checking if secrets are set..."
          if [ -n "$PAT_TOKEN" ]; then echo "PAT_TOKEN is set"; else echo "Error: PAT_TOKEN is not set"; exit 1; fi
          if [ -n "$EC2_HOST" ]; then echo "EC2_HOST is set"; else echo "Error: EC2_HOST is not set"; exit 1; fi
          if [ -n "$EC2_USER" ]; then echo "EC2_USER is set"; else echo "Error: EC2_USER is not set"; exit 1; fi
          if [ -n "$EC2_KEY" ]; then echo "EC2_KEY is set (length: ${#EC2_KEY} characters)"; else echo "Error: EC2_KEY is not set"; exit 1; fi
          if [ -n "$NEXTAUTH_SECRET" ]; then echo "NEXTAUTH_SECRET is set"; else echo "Error: NEXTAUTH_SECRET is not set"; exit 1; fi
          if [ -n "$NEXTAUTH_URL" ]; then echo "NEXTAUTH_URL is set"; else echo "Error: NEXTAUTH_URL is not set"; exit 1; fi

          echo "Testing repository access with PAT_TOKEN..."
          status=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: token $PAT_TOKEN" https://api.github.com/repos/khairiEsprit/sheepskin)
          if [ "$status" -eq 200 ]; then
            echo "Repository access successful (HTTP 200)"
          else
            echo "Repository access failed (HTTP $status)"
            echo "Testing with bearer token format..."
            status2=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer $PAT_TOKEN" https://api.github.com/repos/khairiEsprit/sheepskin)
            if [ "$status2" -eq 200 ]; then
              echo "Repository access successful with Bearer token (HTTP 200)"
            else
              echo "Repository access failed with both token formats (HTTP $status2)"
            fi
          fi

          echo "Testing git clone with PAT_TOKEN..."
          git clone https://$PAT_TOKEN:<EMAIL>/khairiEsprit/sheepskin.git test-clone
          if [ $? -eq 0 ]; then
            echo "Git clone test successful"
            rm -rf test-clone
          else
            echo "Git clone test failed"
          fi

          echo "Repository URL: ${{ env.REPO_URL }}"

      - name: Deploy to EC2
        env:
          EC2_HOST: ${{ secrets.EC2_HOST }}
          EC2_USER: ${{ secrets.EC2_USER }}
          EC2_KEY: ${{ secrets.EC2_KEY }}
          PAT_TOKEN: ${{ secrets.PAT_TOKEN }}
          NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
          NEXTAUTH_URL: ${{ secrets.NEXTAUTH_URL }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
        run: |
          # Create temporary directory for SSH key
          mkdir -p ~/.ssh
          echo "$EC2_KEY" > ~/.ssh/ec2_key.pem
          chmod 600 ~/.ssh/ec2_key.pem

          # Create deployment script with embedded variables
          cat > ~/deploy_script.sh << 'DEPLOY_EOF'
          #!/bin/bash
          set -e

          echo "Starting deployment of Sheepskin Collection Platform..."

          # Set the PostgreSQL password
          DB_PASSWORD="DB_PASSWORD_PLACEHOLDER"

          # Set environment variables (these will be substituted when the script is created)
          NEXTAUTH_SECRET_VAR="NEXTAUTH_SECRET_PLACEHOLDER"
          NEXTAUTH_URL_VAR="NEXTAUTH_URL_PLACEHOLDER"
          PAT_TOKEN_VAR="PAT_TOKEN_PLACEHOLDER"
          DATABASE_URL_VAR="DATABASE_URL_PLACEHOLDER"

          echo "Environment variables loaded:"
          echo "NEXTAUTH_SECRET length: ${#NEXTAUTH_SECRET_VAR}"
          echo "NEXTAUTH_URL length: ${#NEXTAUTH_URL_VAR}"

          # Create app directory
          mkdir -p /home/<USER>/sheepskin-app
          cd /home/<USER>/sheepskin-app

          # Create backup of current deployment
          BACKUP_DIR="/tmp/sheepskin-backup-$(date +%Y%m%d_%H%M%S)"
          if [ -d ".next" ]; then
            echo "Creating backup..."
            mkdir -p "$BACKUP_DIR"
            cp -r .next "$BACKUP_DIR/" 2>/dev/null || true
            cp .env "$BACKUP_DIR/" 2>/dev/null || true
          fi

          # Completely remove and recreate deployment directory
          cd /home/<USER>
          sudo rm -rf sheepskin-app
          mkdir -p sheepskin-app
          cd sheepskin-app

          # Clone repository
          echo "Cloning repository..."
          git clone https://$PAT_TOKEN_VAR:<EMAIL>/khairiEsprit/sheepskin.git .

          # Use existing .env file and preserve DATABASE_URL
          echo "Setting up environment variables..."
          if [ -f "/home/<USER>/.env" ]; then
            echo "Using existing .env file from /home/<USER>/.env..."
            cp /home/<USER>/.env .env
            
            # Extract existing DATABASE_URL and fix hostname/port for local PostgreSQL
            EXISTING_DATABASE_URL=$(grep "^DATABASE_URL=" /home/<USER>/.env | cut -d'=' -f2- | tr -d '"' || echo "")
            
            # Replace Docker hostnames and ports with local PostgreSQL settings, or use default if empty
            if [ -n "$EXISTING_DATABASE_URL" ]; then
              FIXED_DATABASE_URL=$(echo "$EXISTING_DATABASE_URL" | sed 's/@postgres:5432/@localhost:5433/g' | sed 's/@postgres:5433/@localhost:5433/g')
            else
              FIXED_DATABASE_URL="postgresql://postgres:DB_PASSWORD_PLACEHOLDER@localhost:5433/sheepskin"
            fi
            
            # Update environment variables
            sed -i '/^DATABASE_URL=/d' .env 2>/dev/null || true
            sed -i '/^NEXTAUTH_SECRET=/d' .env 2>/dev/null || true
            sed -i '/^NEXTAUTH_URL=/d' .env 2>/dev/null || true
            sed -i '/^NODE_ENV=/d' .env 2>/dev/null || true
            
            echo "DATABASE_URL=\"$FIXED_DATABASE_URL\"" >> .env
            echo "NEXTAUTH_SECRET=\"$NEXTAUTH_SECRET_VAR\"" >> .env
            echo "NEXTAUTH_URL=\"$NEXTAUTH_URL_VAR\"" >> .env
            echo "NODE_ENV=\"production\"" >> .env
            
            # Ensure AWS settings are present
            if ! grep -q "^AWS_REGION=" .env; then
              echo "AWS_REGION=\"us-east-1\"" >> .env
            fi
            if ! grep -q "^AWS_S3_BUCKET_NAME=" .env; then
              echo "AWS_S3_BUCKET_NAME=\"sheepskin-app-images-prod\"" >> .env
            fi
            
            echo "Original DATABASE_URL: $EXISTING_DATABASE_URL"
            echo "Fixed DATABASE_URL: $FIXED_DATABASE_URL"
          else
            echo "No existing .env file found, creating new one..."
            cat > .env << ENV_EOF
          DATABASE_URL="$DATABASE_URL_VAR"
          NEXTAUTH_SECRET="$NEXTAUTH_SECRET_VAR"
          NEXTAUTH_URL="$NEXTAUTH_URL_VAR"
          AWS_REGION="us-east-1"
          AWS_S3_BUCKET_NAME="sheepskin-app-images-prod"
          NODE_ENV="production"
          ENV_EOF
          fi

          echo "Environment file configured"

          # Install and configure local PostgreSQL
          echo "Setting up local PostgreSQL..."

          # Install PostgreSQL if not already installed
          if ! command -v psql &> /dev/null; then
            echo "Installing PostgreSQL..."
            sudo apt update
            sudo apt install -y postgresql postgresql-contrib
          else
            echo "PostgreSQL already installed"
          fi

          # Start PostgreSQL service
          echo "Starting PostgreSQL service..."
          sudo systemctl start postgresql
          sudo systemctl enable postgresql

          # Wait for PostgreSQL to be ready
          echo "Waiting for PostgreSQL to start..."
          for i in {1..10}; do
            if sudo -u postgres psql -c "SELECT 1;" >/dev/null 2>&1; then
              echo "PostgreSQL is ready"
              break
            fi
            if [ $i -eq 10 ]; then
              echo "PostgreSQL failed to start after 10 attempts"
              sudo systemctl status postgresql || true
              exit 1
            fi
            echo "Waiting for PostgreSQL... attempt $i/10"
            sleep 3
          done

          # Configure PostgreSQL to run on port 5433 (different from Docker)
          echo "Configuring PostgreSQL port and authentication..."

          # Find the actual PostgreSQL version and configuration directory
          PG_VERSION=$(sudo -u postgres psql -p 5432 -t -c "SELECT version();" 2>/dev/null | grep -oP "PostgreSQL \K\d+\.\d+" | cut -d. -f1 || echo "")

          if [ -z "$PG_VERSION" ]; then
            # Try to find version from installed packages
            PG_VERSION=$(dpkg -l | grep postgresql-[0-9] | head -1 | grep -oP "postgresql-\K\d+" || echo "")
          fi

          if [ -z "$PG_VERSION" ]; then
            # Default fallback - find any postgresql config directory
            PG_CONFIG_DIR=$(find /etc/postgresql -name "postgresql.conf" -type f | head -1 | xargs dirname 2>/dev/null || echo "")
          else
            PG_CONFIG_DIR="/etc/postgresql/${PG_VERSION}/main"
          fi

          echo "Detected PostgreSQL version: $PG_VERSION"
          echo "Configuration directory: $PG_CONFIG_DIR"

          # Verify configuration directory exists
          if [ ! -d "$PG_CONFIG_DIR" ] || [ ! -f "$PG_CONFIG_DIR/postgresql.conf" ]; then
            echo "PostgreSQL configuration directory not found. Searching for config files..."
            PG_CONFIG_DIR=$(find /etc/postgresql -name "postgresql.conf" -type f | head -1 | xargs dirname 2>/dev/null || echo "")

            if [ -z "$PG_CONFIG_DIR" ] || [ ! -f "$PG_CONFIG_DIR/postgresql.conf" ]; then
              echo "Error: Could not locate PostgreSQL configuration files"
              echo "Available PostgreSQL directories:"
              ls -la /etc/postgresql/ || true
              exit 1
            fi
            echo "Found configuration directory: $PG_CONFIG_DIR"
          fi

          # Update port in postgresql.conf
          echo "Updating PostgreSQL port configuration..."
          sudo sed -i "s/#port = 5432/port = 5433/" "$PG_CONFIG_DIR/postgresql.conf"
          sudo sed -i "s/^port = 5432/port = 5433/" "$PG_CONFIG_DIR/postgresql.conf"

          # Verify port was updated
          if ! sudo grep -q "port = 5433" "$PG_CONFIG_DIR/postgresql.conf"; then
            echo "Adding port configuration to postgresql.conf..."
            echo "port = 5433" | sudo tee -a "$PG_CONFIG_DIR/postgresql.conf"
          fi

          # Update pg_hba.conf for password authentication
          echo "Updating PostgreSQL authentication configuration..."
          if ! sudo grep -q "local.*all.*postgres.*md5" "$PG_CONFIG_DIR/pg_hba.conf"; then
            sudo sed -i "s/local   all             postgres                                peer/local   all             postgres                                md5/" "$PG_CONFIG_DIR/pg_hba.conf"
          fi

          # Also ensure host connections work
          if ! sudo grep -q "host.*all.*postgres.*127.0.0.1/32.*md5" "$PG_CONFIG_DIR/pg_hba.conf"; then
            echo "host    all             postgres        127.0.0.1/32            md5" | sudo tee -a "$PG_CONFIG_DIR/pg_hba.conf"
          fi

          # Set up database user and password
          echo "Setting up database user and password..."

          # Restart PostgreSQL to apply configuration changes
          echo "Restarting PostgreSQL to apply configuration..."
          sudo systemctl restart postgresql
          sleep 10

          # Wait for PostgreSQL to be ready and determine which port to use
          echo "Determining PostgreSQL port..."
          PG_PORT=""

          # First try port 5433 (our configured port)
          for i in {1..15}; do
            if sudo -u postgres psql -p 5433 -c "SELECT 1;" >/dev/null 2>&1; then
              echo "PostgreSQL is ready on port 5433"
              PG_PORT="5433"
              break
            fi
            echo "Waiting for PostgreSQL on port 5433... attempt $i/15"
            sleep 2
          done

          # If port 5433 failed, try default port 5432
          if [ -z "$PG_PORT" ]; then
            echo "Port 5433 not available, trying default port 5432..."
            for i in {1..10}; do
              if sudo -u postgres psql -p 5432 -c "SELECT 1;" >/dev/null 2>&1; then
                echo "PostgreSQL is ready on port 5432"
                PG_PORT="5432"
                break
              fi
              echo "Waiting for PostgreSQL on port 5432... attempt $i/10"
              sleep 2
            done
          fi

          if [ -z "$PG_PORT" ]; then
            echo "PostgreSQL failed to start on both ports 5433 and 5432"
            echo "Checking PostgreSQL status and configuration..."
            sudo systemctl status postgresql || true
            sudo grep "port" "$PG_CONFIG_DIR/postgresql.conf" || true
            sudo netstat -tlnp | grep postgres || true
            exit 1
          fi

          echo "Using PostgreSQL on port: $PG_PORT"

          # Set the postgres user password
          echo "Setting postgres user password..."
          sudo -u postgres psql -p $PG_PORT -c "ALTER USER postgres PASSWORD '$DB_PASSWORD';" || {
            echo "Failed to set password. Checking PostgreSQL status..."
            sudo systemctl status postgresql || true
            sudo -u postgres psql -p $PG_PORT -c "SELECT version();" || true
            exit 1
          }

          # Create database
          echo "Creating sheepskin database..."
          sudo -u postgres createdb -p $PG_PORT sheepskin 2>/dev/null || {
            echo "Database creation failed or database already exists"
            # Check if database exists
            if sudo -u postgres psql -p $PG_PORT -lqt | cut -d \| -f 1 | grep -qw sheepskin; then
              echo "Database 'sheepskin' already exists"
            else
              echo "Failed to create database. Checking PostgreSQL status..."
              sudo -u postgres psql -p $PG_PORT -l || true
              exit 1
            fi
          }

          # Update DATABASE_URL for local PostgreSQL
          echo "Updating DATABASE_URL for local PostgreSQL..."
          LOCAL_DB_URL="postgresql://postgres:$DB_PASSWORD@localhost:$PG_PORT/sheepskin"
          sed -i '/^DATABASE_URL=/d' .env 2>/dev/null || true
          echo "DATABASE_URL=\"$LOCAL_DB_URL\"" >> .env
          echo "Updated DATABASE_URL: $LOCAL_DB_URL"

          # Verify PostgreSQL connection before proceeding
          echo "Testing PostgreSQL connection..."
          for i in {1..5}; do
            if PGPASSWORD="$DB_PASSWORD" psql -h localhost -p $PG_PORT -U postgres -d sheepskin -c "SELECT 1;" > /dev/null 2>&1; then
              echo "✅ PostgreSQL connection successful"
              break
            fi
            if [ $i -eq 5 ]; then
              echo "❌ Failed to connect to PostgreSQL after 5 attempts"
              echo "Debugging PostgreSQL connection..."
              echo "Using port: $PG_PORT"
              echo "PostgreSQL service status:"
              sudo systemctl status postgresql || true
              echo "PostgreSQL processes:"
              sudo ps aux | grep postgres || true
              echo "PostgreSQL listening ports:"
              sudo netstat -tlnp | grep postgres || true
              echo "PostgreSQL configuration:"
              sudo grep -E "^(port|listen_addresses)" "$PG_CONFIG_DIR/postgresql.conf" || true
              echo "Available databases:"
              sudo -u postgres psql -p $PG_PORT -l || true
              echo "Testing connection without password:"
              sudo -u postgres psql -p $PG_PORT -d sheepskin -c "SELECT 1;" || true
              exit 1
            fi
            echo "Connection attempt $i failed, retrying in 5 seconds..."
            sleep 5
          done

          # Install dependencies
          echo "Installing dependencies..."
          npm ci --omit=dev

          # Generate Prisma client
          echo "Generating Prisma client..."
          npx prisma generate

          # Reset database and migrations completely
          echo "Checking migration status and updating database..."

          # Check current migration status
          echo "Checking current migration status..."
          MIGRATION_STATUS=$(npx prisma migrate status 2>&1 || echo "NO_MIGRATIONS")

          if echo "$MIGRATION_STATUS" | grep -q "Database schema is up to date"; then
            echo "✅ Database is up to date, checking for new migrations..."
            npx prisma migrate deploy || echo "No new migrations to deploy"
          elif echo "$MIGRATION_STATUS" | grep -q "following migration have not yet been applied"; then
            echo "📦 New migrations found, applying them..."
            npx prisma migrate deploy
          elif echo "$MIGRATION_STATUS" | grep -q "failed migrations" || echo "$MIGRATION_STATUS" | grep -q "drift detected"; then
            echo "⚠️ Migration issues detected, resetting migration state..."
            
            # Mark failed migrations as resolved
            echo "Resolving migration conflicts..."
            npx prisma migrate resolve --applied 20250624194420_init_with_donations || true
            npx prisma migrate resolve --applied 20250625093753_add_collection_model || true
            npx prisma migrate resolve --applied 20250705135600_add_coordinates_to_donations || true
            npx prisma migrate resolve --applied 20250724095808_add_images_to_donations || true
            
            # Deploy any remaining migrations
            npx prisma migrate deploy || echo "No additional migrations to deploy"
          else
            echo "🔄 No migration history found, setting up fresh database..."
            
            # Drop and recreate the database for fresh start
            sudo -u postgres psql -p 5433 -c "DROP DATABASE IF EXISTS sheepskin;" || true
            sudo -u postgres psql -p 5433 -c "CREATE DATABASE sheepskin;" || true
            
            # Deploy all migrations from scratch
            npx prisma migrate deploy
          fi

          # Log the final migration status
          echo "Final migration status:"
          npx prisma migrate status || true

          # Validate database connection from app perspective
          echo "Validating database connection from application..."
          npx prisma db pull --force || echo "DB pull failed, but continuing..."

          # Test Prisma client connection
          echo "Testing Prisma client connection..."
          node -e "
            const { PrismaClient } = require('@prisma/client');
            const prisma = new PrismaClient({
              log: ['error', 'warn'],
            });
            prisma.\$connect()
              .then(() => {
                console.log('✅ Prisma client connection successful');
                return prisma.\$disconnect();
              })
              .catch((e) => {
                console.error('❌ Prisma client connection failed:', e.message);
                console.error('Full error:', e);
                process.exit(1);
              });
          " || {
            echo "Prisma connection test failed. Debugging..."

            echo "Current DATABASE_URL in .env:"
            grep "DATABASE_URL" .env || true

            echo "Testing direct database connection..."
            PGPASSWORD="$DB_PASSWORD" psql -h localhost -p 5433 -U postgres -d sheepskin -c "SELECT 1;" || {
              echo "Direct connection failed. Checking PostgreSQL status..."
              sudo systemctl status postgresql || true
              sudo -u postgres psql -p 5433 -c "SELECT version();" || true
              echo "Checking if database exists:"
              sudo -u postgres psql -p 5433 -l | grep sheepskin || true
              exit 1
            }

            echo "Direct connection works, but Prisma fails. Checking Prisma schema..."
            npx prisma db pull --force || echo "DB pull failed"
            npx prisma validate || echo "Schema validation failed"
            exit 1
          }

          # Ensure database has proper permissions
          echo "Setting up database permissions..."
          sudo -u postgres psql -p 5433 -d sheepskin -c "
            GRANT ALL PRIVILEGES ON DATABASE sheepskin TO postgres;
            GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
            GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;
          " || true

          # Build the application
          echo "Building application..."
          npm run build

          # Install PM2 if needed
          if ! command -v pm2 &> /dev/null; then
            echo "Installing PM2..."
            sudo npm install -g pm2
          fi

          # Restart application
          echo "Restarting application..."
          pm2 restart sheepskin-app || pm2 start npm --name "sheepskin-app" -- start

          # Wait for app to start and verify environment
          echo "Waiting for application to start..."
          sleep 15

          # Check if app is reading the correct DATABASE_URL
          echo "Verifying application environment..."
          pm2 logs sheepskin-app --lines 10 || true

          # Test if app can connect to database
          echo "Testing application database connectivity..."
          curl -f http://localhost:3000/api/auth/providers 2>/dev/null || {
            echo "App not responding on /api/auth/providers, checking status..."
            pm2 status sheepskin-app || true
            pm2 logs sheepskin-app --lines 20 || true
          }

          # Ensure Docker containers restart on boot
          echo "Configuring services for auto-restart..."
          sudo systemctl enable postgresql

          # Save PM2 configuration
          pm2 save

          # Health check
          echo "Performing health check..."
          sleep 10
          for i in {1..5}; do
            if curl -f http://localhost:3000 >/dev/null 2>&1; then
              echo "Health check passed"
              break
            fi
            if [ $i -eq 5 ]; then
              echo "Health check failed after 5 attempts"
              exit 1
            fi
            echo "Health check attempt $i failed, retrying in 10 seconds..."
            sleep 10
          done

          echo "Deployment completed successfully!"
          DEPLOY_EOF

          # Replace placeholders with actual values
          sed -i "s|NEXTAUTH_SECRET_PLACEHOLDER|$NEXTAUTH_SECRET|g" ~/deploy_script.sh
          sed -i "s|NEXTAUTH_URL_PLACEHOLDER|$NEXTAUTH_URL|g" ~/deploy_script.sh
          sed -i "s|PAT_TOKEN_PLACEHOLDER|$PAT_TOKEN|g" ~/deploy_script.sh
          sed -i "s|DATABASE_URL_PLACEHOLDER|$DATABASE_URL|g" ~/deploy_script.sh
          sed -i "s|DB_PASSWORD_PLACEHOLDER|$DB_PASSWORD|g" ~/deploy_script.sh

          # Execute deployment on EC2
          scp -o StrictHostKeyChecking=no -i ~/.ssh/ec2_key.pem ~/deploy_script.sh $EC2_USER@$EC2_HOST:/tmp/
          ssh -o StrictHostKeyChecking=no -i ~/.ssh/ec2_key.pem $EC2_USER@$EC2_HOST "chmod +x /tmp/deploy_script.sh && /tmp/deploy_script.sh"

      - name: Rollback on deployment failure
        if: failure()
        env:
          EC2_HOST: ${{ secrets.EC2_HOST }}
          EC2_USER: ${{ secrets.EC2_USER }}
          EC2_KEY: ${{ secrets.EC2_KEY }}
        run: |
          # Create temporary directory for SSH key
          mkdir -p ~/.ssh
          echo "$EC2_KEY" > ~/.ssh/ec2_key.pem
          chmod 600 ~/.ssh/ec2_key.pem

          cat > ~/rollback_script.sh << 'ROLLBACK_SCRIPT'
          #!/bin/bash
          set -e

          echo "Performing rollback..."

          if [ -d "/home/<USER>/sheepskin-app" ]; then
            cd /home/<USER>/sheepskin-app
            
            # Look for the most recent backup
            LATEST_BACKUP=$(ls -t /tmp/sheepskin-backup-* 2>/dev/null | head -1)
            if [ -n "$LATEST_BACKUP" ] && [ -d "$LATEST_BACKUP/.next" ]; then
              echo "Restoring from backup: $LATEST_BACKUP"
              rm -rf .next 2>/dev/null || true
              cp -r "$LATEST_BACKUP/.next" .next
              if [ -f "$LATEST_BACKUP/.env" ]; then
                cp "$LATEST_BACKUP/.env" .env
              fi
              pm2 restart sheepskin-app
              echo "Rollback completed"
            else
              echo "No backup found, reverting to previous commit..."
              git reset --hard HEAD~1
              npm ci --omit=dev
              npm run build
              pm2 restart sheepskin-app
              echo "Rollback to previous commit completed"
            fi
          else
            echo "Application directory not found, rollback not possible"
          fi
          ROLLBACK_SCRIPT

          scp -o StrictHostKeyChecking=no -i ~/.ssh/ec2_key.pem ~/rollback_script.sh $EC2_USER@$EC2_HOST:/tmp/
          ssh -o StrictHostKeyChecking=no -i ~/.ssh/ec2_key.pem $EC2_USER@$EC2_HOST "chmod +x /tmp/rollback_script.sh && /tmp/rollback_script.sh"
