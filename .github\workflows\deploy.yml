name: CI/CD Pipeline for Sheepskin Collection Platform

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

env:
  NODE_VERSION: "18"
  APP_NAME: "sheepskin-app"
  APP_DIRECTORY: "/home/<USER>/sheepskin-app"
  REPO_URL: "https://github.com/khairiEsprit/sheepskin.git"

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: sheepskin_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Set up test environment
        run: |
          cat > .env.test << EOF
          DATABASE_URL="postgresql://postgres:postgres@localhost:5432/sheepskin_test"
          NEXTAUTH_SECRET="test-secret-key-for-ci"
          NEXTAUTH_URL="http://localhost:3000"
          NODE_ENV="test"
          EOF

      - name: Generate Prisma client
        run: npx prisma generate

      - name: Run database migrations
        run: npx prisma migrate deploy
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/sheepskin_test"

      - name: Run TypeScript check
        run: npx tsc --noEmit

      - name: Run tests
        run: npm test
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/sheepskin_test"

      - name: Build app
        run: npm run build
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/sheepskin_test"
          NEXTAUTH_SECRET: "test-secret-key-for-ci"
          NEXTAUTH_URL: "http://localhost:3000"

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: next-build
          path: |
            .next/
            package.json
            package-lock.json
            prisma/
            public/
          retention-days: 1

      - name: Run dependency security audit
        run: npm audit --audit-level moderate

  deploy:
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: next-build

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Deploy to EC2
        env:
          EC2_HOST: ${{ secrets.EC2_HOST }}
          EC2_USER: ${{ secrets.EC2_USER }}
          EC2_KEY: ${{ secrets.EC2_KEY }}
          PAT_TOKEN: ${{ secrets.PAT_TOKEN }}
          NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
          NEXTAUTH_URL: ${{ secrets.NEXTAUTH_URL }}
          DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
        run: |
          # Create temporary directory for SSH key
          mkdir -p ~/.ssh
          echo "$EC2_KEY" > ~/.ssh/ec2_key.pem
          chmod 600 ~/.ssh/ec2_key.pem

          # Create simplified deployment script
          cat > ~/deploy_script.sh << 'DEPLOY_EOF'
          #!/bin/bash
          set -e

          echo "Starting deployment of Sheepskin Collection Platform..."

          # Set environment variables (these will be substituted when the script is created)
          NEXTAUTH_SECRET_VAR="NEXTAUTH_SECRET_PLACEHOLDER"
          NEXTAUTH_URL_VAR="NEXTAUTH_URL_PLACEHOLDER"
          PAT_TOKEN_VAR="PAT_TOKEN_PLACEHOLDER"
          DB_PASSWORD="DB_PASSWORD_PLACEHOLDER"

          # Create app directory and clone repository
          cd /home/<USER>
          rm -rf sheepskin-app
          mkdir -p sheepskin-app
          cd sheepskin-app

          echo "Cloning repository..."
          git clone https://$PAT_TOKEN_VAR:<EMAIL>/khairiEsprit/sheepskin.git .

          # Copy existing .env file
          echo "Setting up environment variables..."
          if [ -f "/home/<USER>/.env" ]; then
            cp /home/<USER>/.env .env

            # Update environment variables for production
            sed -i '/^NEXTAUTH_SECRET=/d' .env 2>/dev/null || true
            sed -i '/^NEXTAUTH_URL=/d' .env 2>/dev/null || true
            sed -i '/^NODE_ENV=/d' .env 2>/dev/null || true

            echo "NEXTAUTH_SECRET=\"$NEXTAUTH_SECRET_VAR\"" >> .env
            echo "NEXTAUTH_URL=\"$NEXTAUTH_URL_VAR\"" >> .env
            echo "NODE_ENV=\"production\"" >> .env
          else
            echo "No existing .env file found"
            exit 1
          fi

          # Install PostgreSQL if needed
          if ! command -v psql &> /dev/null; then
            echo "Installing PostgreSQL..."
            sudo apt update
            sudo apt install -y postgresql postgresql-contrib
          fi

          # Start PostgreSQL service
          sudo systemctl start postgresql
          sudo systemctl enable postgresql

          # Wait for PostgreSQL to be ready
          echo "Waiting for PostgreSQL to start..."
          sleep 5

          # Find PostgreSQL configuration
          PG_VERSION=$(dpkg -l | grep postgresql-[0-9] | head -1 | grep -oP "postgresql-\K\d+" || echo "15")
          PG_CONFIG_DIR="/etc/postgresql/${PG_VERSION}/main"

          # Configure PostgreSQL authentication first (before setting password)
          if [ -f "$PG_CONFIG_DIR/pg_hba.conf" ]; then
            echo "Configuring PostgreSQL authentication..."
            # Backup original config
            sudo cp "$PG_CONFIG_DIR/pg_hba.conf" "$PG_CONFIG_DIR/pg_hba.conf.backup"

            # Update authentication method for local connections
            sudo sed -i "s/local   all             postgres                                peer/local   all             postgres                                trust/" "$PG_CONFIG_DIR/pg_hba.conf"
            sudo sed -i "s/local   all             all                                     peer/local   all             all                                     trust/" "$PG_CONFIG_DIR/pg_hba.conf"

            # Restart PostgreSQL to apply auth changes
            sudo systemctl restart postgresql
            sleep 5
          fi

          # Determine which port PostgreSQL is running on
          PG_PORT="5432"
          echo "Using PostgreSQL on port: $PG_PORT"

          # Set postgres password and create database (using trust authentication)
          echo "Setting up database..."
          sudo -u postgres psql -p $PG_PORT -c "ALTER USER postgres PASSWORD '$DB_PASSWORD';"
          sudo -u postgres createdb -p $PG_PORT sheepskin 2>/dev/null || echo "Database exists"

          # Now switch back to password authentication
          if [ -f "$PG_CONFIG_DIR/pg_hba.conf" ]; then
            echo "Switching to password authentication..."
            sudo sed -i "s/local   all             postgres                                trust/local   all             postgres                                md5/" "$PG_CONFIG_DIR/pg_hba.conf"
            sudo sed -i "s/local   all             all                                     trust/local   all             all                                     md5/" "$PG_CONFIG_DIR/pg_hba.conf"

            # Add host connection for localhost
            if ! sudo grep -q "host.*all.*postgres.*127.0.0.1/32.*md5" "$PG_CONFIG_DIR/pg_hba.conf"; then
              echo "host    all             postgres        127.0.0.1/32            md5" | sudo tee -a "$PG_CONFIG_DIR/pg_hba.conf"
            fi

            sudo systemctl restart postgresql
            sleep 5
          fi

          # Update DATABASE_URL in .env
          LOCAL_DB_URL="postgresql://postgres:$DB_PASSWORD@localhost:$PG_PORT/sheepskin"
          sed -i '/^DATABASE_URL=/d' .env
          echo "DATABASE_URL=\"$LOCAL_DB_URL\"" >> .env

          # Test database connection
          echo "Testing database connection..."
          for i in {1..5}; do
            if PGPASSWORD="$DB_PASSWORD" psql -h localhost -p $PG_PORT -U postgres -d sheepskin -c "SELECT 1;" >/dev/null 2>&1; then
              echo "✅ Database connection successful"
              break
            fi
            if [ $i -eq 5 ]; then
              echo "❌ Database connection failed after 5 attempts"
              echo "Checking PostgreSQL status..."
              sudo systemctl status postgresql
              echo "Checking if database exists..."
              sudo -u postgres psql -p $PG_PORT -l | grep sheepskin || true
              exit 1
            fi
            echo "Connection attempt $i failed, retrying..."
            sleep 3
          done

          # Install dependencies and build
          echo "Installing dependencies..."
          npm ci --omit=dev

          echo "Generating Prisma client..."
          npx prisma generate

          echo "Running database migrations..."
          npx prisma migrate deploy || npx prisma db push

          echo "Building application..."
          npm run build

          # Install PM2 and start application
          if ! command -v pm2 &> /dev/null; then
            sudo npm install -g pm2
          fi

          pm2 restart sheepskin-app || pm2 start npm --name "sheepskin-app" -- start
          pm2 save

          echo "Deployment completed successfully!"
          DEPLOY_EOF

          # Replace placeholders with actual values
          sed -i "s|NEXTAUTH_SECRET_PLACEHOLDER|$NEXTAUTH_SECRET|g" ~/deploy_script.sh
          sed -i "s|NEXTAUTH_URL_PLACEHOLDER|$NEXTAUTH_URL|g" ~/deploy_script.sh
          sed -i "s|PAT_TOKEN_PLACEHOLDER|$PAT_TOKEN|g" ~/deploy_script.sh
          sed -i "s|DB_PASSWORD_PLACEHOLDER|$DB_PASSWORD|g" ~/deploy_script.sh

          # Execute deployment on EC2
          scp -o StrictHostKeyChecking=no -i ~/.ssh/ec2_key.pem ~/deploy_script.sh $EC2_USER@$EC2_HOST:/tmp/
          ssh -o StrictHostKeyChecking=no -i ~/.ssh/ec2_key.pem $EC2_USER@$EC2_HOST "chmod +x /tmp/deploy_script.sh && /tmp/deploy_script.sh"
