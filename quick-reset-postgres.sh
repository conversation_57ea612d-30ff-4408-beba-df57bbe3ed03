#!/bin/bash
set -e

echo "🔄 Quick PostgreSQL user/database reset..."

# Get PostgreSQL version and config directory
PG_VERSION=$(dpkg -l | grep postgresql-[0-9] | head -1 | grep -oP "postgresql-\K\d+" || echo "15")
PG_CONFIG_DIR="/etc/postgresql/${PG_VERSION}/main"

echo "PostgreSQL version: $PG_VERSION"
echo "Config directory: $PG_CONFIG_DIR"

# Stop PostgreSQL
sudo systemctl stop postgresql

# Configure authentication to trust temporarily
echo "Configuring temporary trust authentication..."
sudo cp "$PG_CONFIG_DIR/pg_hba.conf" "$PG_CONFIG_DIR/pg_hba.conf.backup" 2>/dev/null || true

# Set all local connections to trust
sudo sed -i 's/local   all             postgres                                peer/local   all             postgres                                trust/' "$PG_CONFIG_DIR/pg_hba.conf"
sudo sed -i 's/local   all             postgres                                md5/local   all             postgres                                trust/' "$PG_CONFIG_DIR/pg_hba.conf"
sudo sed -i 's/local   all             all                                     peer/local   all             all                                     trust/' "$PG_CONFIG_DIR/pg_hba.conf"
sudo sed -i 's/local   all             all                                     md5/local   all             all                                     trust/' "$PG_CONFIG_DIR/pg_hba.conf"

# Start PostgreSQL
sudo systemctl start postgresql
sleep 5

# Drop and recreate database
echo "Recreating sheepskin database..."
sudo -u postgres psql -c "DROP DATABASE IF EXISTS sheepskin;"
sudo -u postgres psql -c "CREATE DATABASE sheepskin;"

# Reset postgres user password
echo "Resetting postgres user password..."
NEW_PASSWORD="Kingragnar1\$"
sudo -u postgres psql -c "ALTER USER postgres PASSWORD '$NEW_PASSWORD';"

# Grant all privileges
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE sheepskin TO postgres;"

# Configure for password authentication
echo "Configuring password authentication..."
sudo sed -i 's/local   all             postgres                                trust/local   all             postgres                                md5/' "$PG_CONFIG_DIR/pg_hba.conf"
sudo sed -i 's/local   all             all                                     trust/local   all             all                                     md5/' "$PG_CONFIG_DIR/pg_hba.conf"

# Add host connections if not present
if ! sudo grep -q "host.*all.*postgres.*127.0.0.1/32.*md5" "$PG_CONFIG_DIR/pg_hba.conf"; then
    echo "host    all             postgres        127.0.0.1/32            md5" | sudo tee -a "$PG_CONFIG_DIR/pg_hba.conf"
fi

# Restart PostgreSQL
sudo systemctl restart postgresql
sleep 5

# Test connection
echo "Testing database connection..."
if PGPASSWORD="$NEW_PASSWORD" psql -h localhost -U postgres -d sheepskin -c "SELECT 1;" >/dev/null 2>&1; then
    echo "✅ Database connection successful!"
    echo "✅ PostgreSQL reset complete!"
    echo ""
    echo "Database details:"
    echo "- Host: localhost"
    echo "- Port: 5432"
    echo "- Database: sheepskin"
    echo "- Username: postgres"
    echo "- Password: Kingragnar1\$"
    echo ""
    echo "DATABASE_URL: postgresql://postgres:Kingragnar1\$@localhost:5432/sheepskin"
else
    echo "❌ Database connection failed!"
    echo "Checking PostgreSQL status..."
    sudo systemctl status postgresql
    exit 1
fi
